{"version": "1.0.0", "source": "./index.html", "type": "module", "name": "anima-project", "description": "A React project automatically generated by <PERSON><PERSON> using the Shadcn UI library", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-slot": "^1.1.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "dotenv": "^16.5.0", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "pg": "^8.16.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "tailwind-merge": "2.5.4"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "4.3.4", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "vite": "6.0.4"}, "alias": {"@/*": "./src/components/ui/$1"}}