import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import * as Dialog from "@radix-ui/react-dialog";
import { authService } from "../../services/auth";
import { apiService, Domain } from "../../services/api";



export const Dashboard = (): JSX.Element => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [newDomain, setNewDomain] = useState("");
  const [domains, setDomains] = useState<Domain[]>([]);
  const [user, setUser] = useState(authService.getUser());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check authentication
    if (!authService.isAuthenticated()) {
      navigate('/');
      return;
    }

    // Load domains from API
    loadDomains();
  }, [navigate]);

  const loadDomains = async () => {
    try {
      setLoading(true);
      const domainsData = await apiService.getDomains();
      setDomains(domainsData);
    } catch (error) {
      console.error('Error loading domains:', error);
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    { label: "DDoS", count: 0 },
    { label: "SQL Injection", count: 0 },
    { label: "XSS", count: 0 }
  ];

  const handleAddDomain = async () => {
    if (newDomain.trim()) {
      try {
        const newDomainData = await apiService.addDomain(newDomain.trim());
        if (newDomainData) {
          await loadDomains();
          setNewDomain("");
          setIsOpen(false);
        } else {
          alert('Failed to add domain. Please try again.');
        }
      } catch (error) {
        console.error('Error adding domain:', error);
        alert('Failed to add domain. Please try again.');
      }
    }
  };

  const handleLogout = () => {
    authService.logout();
  };

  return (
    <div className="min-h-screen bg-white">
      <header className="flex justify-between items-center px-8 py-4 border-b">
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
            <span className="text-white font-bold text-lg">S</span>
          </div>
          <span className="font-['Poppins'] font-semibold text-2xl">SYRA</span>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
              I
            </div>
            <span>{user?.name || 'User'}</span>
          </div>
          <Button
            variant="default"
            className="bg-blue-500 hover:bg-blue-600 text-white"
            onClick={handleLogout}
          >
            Log Out
          </Button>
        </div>
      </header>

      <main className="p-8">
        <h1 className="text-2xl font-semibold mb-8">Total Number of Attacks Detected</h1>

        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        )}
        
        <div className="grid grid-cols-3 gap-8 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-50 p-8 rounded-lg text-center">
              <div className="text-4xl font-bold mb-2">{stat.count}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">My Domain</h2>
          <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
            <Dialog.Trigger asChild>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">Add New Domain</Button>
            </Dialog.Trigger>
            <Dialog.Portal>
              <Dialog.Overlay className="fixed inset-0 bg-black/50" />
              <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[400px]">
                <div className="flex justify-between items-center mb-4">
                  <Dialog.Title className="text-xl font-semibold">Add New Domain</Dialog.Title>
                  <Dialog.Close className="text-gray-400 hover:text-gray-600">
                    <span className="text-xl">×</span>
                  </Dialog.Close>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Domain Link
                  </label>
                  <input
                    type="text"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    placeholder="E.g. trenteknologimobile.com"
                    className="w-full px-3 py-2 border rounded-md"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={handleAddDomain}
                  >
                    Save
                  </Button>
                </div>
              </Dialog.Content>
            </Dialog.Portal>
          </Dialog.Root>
        </div>

        {domains.length === 0 && !loading ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-4">No domains added yet</div>
            <p className="text-gray-500">Click "Add New Domain" to start monitoring your first domain</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {domains.map((domain, index) => (
              <div
                key={domain.id || index}
                className="bg-gray-50 p-6 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors border"
                onClick={() => navigate(`/domain/${domain.domain_name}`)}
              >
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                    S
                  </div>
                  <span className="font-medium">{domain.domain_name}</span>
                </div>
                
                <div>
                  <h3 className="font-medium mb-2">Security Log:</h3>
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600">
                      [{new Date(domain.created_at).toLocaleDateString()}] Domain monitoring started
                    </div>
                    <div className="text-sm text-gray-600">
                      [{new Date().toLocaleDateString()}] No threats detected - monitoring active
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>

      <footer className="text-center py-6 text-gray-600 border-t">
        Copyright 2025 I Nyoman Darmayoga - PUI
      </footer>
    </div>
  );
};