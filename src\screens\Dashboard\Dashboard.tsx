import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import * as Dialog from "@radix-ui/react-dialog";
import { authService } from "../../services/auth";
import { apiService, Domain } from "../../services/api";
import { logger } from "../../services/logger";



export const Dashboard = (): JSX.Element => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [newDomain, setNewDomain] = useState("");
  const [domains, setDomains] = useState<Domain[]>([]);
  const [user, setUser] = useState(authService.getUser());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    logger.logComponent('Dashboard', 'mounted');
    
    // Check authentication
    if (!authService.isAuthenticated()) {
      logger.warn('Unauthenticated user redirected from dashboard', 'dashboard');
      navigate('/');
      return;
    }
    
    logger.info('Dashboard loaded for authenticated user', 'dashboard');
    // Load domains from API
    loadDomains();
    
    return () => {
      logger.logComponent('Dashboard', 'unmounted');
    };
  }, [navigate]);

  const loadDomains = async () => {
    const startTime = performance.now();
    try {
      logger.debug('Loading domains', 'dashboard');
      setLoading(true);
      const domainsData = await apiService.getDomains();
      setDomains(domainsData);
      
      const duration = performance.now() - startTime;
      logger.logPerformance('loadDomains', duration);
      logger.info(`Loaded ${domainsData.length} domains`, 'dashboard', { count: domainsData.length });
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error('Error loading domains', 'dashboard', error as Error, { duration });
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    { label: "DDoS", count: 0 },
    { label: "SQL Injection", count: 0 },
    { label: "XSS", count: 0 }
  ];

  const handleAddDomain = async () => {
    if (!newDomain.trim()) {
      logger.warn('Attempted to add empty domain', 'dashboard');
      return;
    }
    
    const startTime = performance.now();
    const domainName = newDomain.trim();
    
    try {
      logger.info(`Adding new domain: ${domainName}`, 'dashboard');
      const newDomainData = await apiService.addDomain(domainName);
      if (newDomainData) {
        await loadDomains();
        setNewDomain("");
        setIsOpen(false);
        
        const duration = performance.now() - startTime;
        logger.logPerformance('handleAddDomain', duration);
        logger.info(`Successfully added domain: ${domainName}`, 'dashboard', {
          domainId: newDomainData.id,
          duration
        });
      } else {
        logger.warn(`Failed to add domain: ${domainName}`, 'dashboard');
        alert('Failed to add domain. Please try again.');
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error adding domain: ${domainName}`, 'dashboard', error as Error, {
        domainName,
        duration
      });
      alert('Failed to add domain. Please try again.');
    }
  };

  const handleLogout = () => {
    logger.info('User initiated logout from dashboard', 'dashboard');
    authService.logout();
  };

  return (
    <div className="min-h-screen bg-white">
      <header className="flex justify-between items-center px-8 py-4 border-b">
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
            <span className="text-white font-bold text-lg">S</span>
          </div>
          <span className="font-['Poppins'] font-semibold text-2xl">SYRA</span>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
              {user?.name?.charAt(0) || 'I'}
            </div>
            <span className="font-medium">{user?.name || 'I Nyoman Darmayoga'}</span>
          </div>
          <Button
            variant="default"
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
            onClick={handleLogout}
          >
            Log Out
          </Button>
        </div>
      </header>

      <main className="p-8">
        <h1 className="text-2xl font-semibold mb-8">Total Number of Attacks Detected</h1>

        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        )}
        
        <div className="grid grid-cols-3 gap-8 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-50 p-8 rounded-lg text-center">
              <div className="text-4xl font-bold mb-2">{stat.count}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Alert Notification - sesuai desain */}
        {domains.some(d => d.hasAlert) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3">
            <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
            <div>
              <div className="font-medium text-red-800">SQL Injection Detected</div>
              <div className="text-red-700 text-sm">
                The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">My Domain</h2>
          <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
            <Dialog.Trigger asChild>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">Add New Domain</Button>
            </Dialog.Trigger>
            <Dialog.Portal>
              <Dialog.Overlay className="fixed inset-0 bg-black/50" />
              <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[400px]">
                <div className="flex justify-between items-center mb-4">
                  <Dialog.Title className="text-xl font-semibold">Add New Domain</Dialog.Title>
                  <Dialog.Close className="text-gray-400 hover:text-gray-600">
                    <span className="text-xl">×</span>
                  </Dialog.Close>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Domain Link
                  </label>
                  <input
                    type="text"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    placeholder="E.g. trenteknologimobile.com"
                    className="w-full px-3 py-2 border rounded-md"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={handleAddDomain}
                  >
                    Save
                  </Button>
                </div>
              </Dialog.Content>
            </Dialog.Portal>
          </Dialog.Root>
        </div>

        {domains.length === 0 && !loading ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-4">No domains added yet</div>
            <p className="text-gray-500">Click "Add New Domain" to start monitoring your first domain</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {domains.map((domain, index) => (
              <div
                key={domain.id || index}
                className="bg-white border rounded-lg p-4 shadow-sm hover:shadow-md transition-all cursor-pointer"
                onClick={() => navigate(`/domain/${domain.domain_name}`)}
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                    S
                  </div>
                  <span className="font-medium text-base truncate">{domain.domain_name}</span>
                </div>

                <div>
                  <h3 className="font-medium mb-2 text-sm text-gray-700">Security Log:</h3>
                  <div className="space-y-1">
                    <div className="text-xs text-gray-500 truncate">
                      [2025-05-1 10:22:47] No threats detected - nor...
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      [2025-05-1 10:22:46] No threats detected - nor...
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      [2025-05-1 10:22:43] Clean traffic observed - no...
                    </div>
                  </div>
                </div>

                {/* Add alert for first domain to match design */}
                {index === 0 && (
                  <div className="mt-3 bg-red-50 border border-red-200 rounded p-2">
                    <div className="flex items-center gap-2 text-red-600 text-sm font-medium">
                      <span className="w-1.5 h-1.5 bg-red-500 rounded-full"></span>
                      SQL Injection Detected
                    </div>
                    <p className="text-red-700 text-xs mt-1">
                      The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </main>

      <footer className="text-center py-6 text-gray-600 border-t">
        Copyright 2025 I Nyoman Darmayoga - PUI
      </footer>
    </div>
  );
};