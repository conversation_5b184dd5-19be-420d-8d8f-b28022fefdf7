import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import * as Dialog from "@radix-ui/react-dialog";
import { authService } from "../../services/auth";
import { apiService, Domain } from "../../services/api";

interface DomainCard {
  domain: string;
  logs: string[];
  hasAlert?: boolean;
  alertMessage?: string;
}

export const Dashboard = (): JSX.Element => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [newDomain, setNewDomain] = useState("");
  const [domains, setDomains] = useState<DomainCard[]>([]);
  const [realDomains, setRealDomains] = useState<Domain[]>([]);
  const [user, setUser] = useState(authService.getUser());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check authentication
    if (!authService.isAuthenticated()) {
      navigate('/');
      return;
    }

    // Load domains from API
    loadDomains();
  }, [navigate]);

  const loadDomains = async () => {
    try {
      setLoading(true);
      const domainsData = await apiService.getDomains();
      setRealDomains(domainsData);

      // Convert to DomainCard format for display
      const domainCards: DomainCard[] = domainsData.map(domain => ({
        domain: domain.domain_name,
        logs: [
          `[${new Date(domain.created_at).toLocaleString()}] Domain monitoring started`,
          `[${new Date().toLocaleString()}] No threats detected - normal activity`,
          `[${new Date().toLocaleString()}] Clean traffic observed - no suspicious patterns`
        ],
        hasAlert: Math.random() > 0.7, // Random alert for demo
        alertMessage: "The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack"
      }));

      setDomains(domainCards);
    } catch (error) {
      console.error('Error loading domains:', error);
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    { label: "DDoS", count: 0 },
    { label: "SQL Injection", count: 0 },
    { label: "XSS", count: 0 }
  ];

  const handleAddDomain = async () => {
    if (newDomain.trim()) {
      try {
        const newDomainData = await apiService.addDomain(newDomain.trim());
        if (newDomainData) {
          await loadDomains(); // Reload domains from API
          setNewDomain("");
          setIsOpen(false);
        } else {
          alert('Failed to add domain. Please try again.');
        }
      } catch (error) {
        console.error('Error adding domain:', error);
        alert('Failed to add domain. Please try again.');
      }
    }
  };

  const handleLogout = () => {
    authService.logout();
  };

  return (
    <div className="min-h-screen bg-white">
      <header className="flex justify-between items-center px-8 py-4 border-b">
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C8.5 2 6 4.5 6 8c0 2.5 1.5 4.5 3.5 5.5L8 16c-1 1-1 2.5 0 3.5s2.5 1 3.5 0L13 18c1.5-1 2.5-2.5 2.5-4.5V8c0-3.5-2.5-6-6-6zm0 2c2.5 0 4 1.5 4 4v5.5c0 1.5-1 2.5-2 3l-1.5 1.5c-.5.5-1.5.5-2 0s-.5-1.5 0-2L12 14c-1.5-1-2.5-2.5-2.5-4.5C9.5 6.5 10.5 4 12 4z"/>
            </svg>
          </div>
          <span className="font-['Poppins'] font-semibold text-2xl">SYRA</span>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
              I
            </div>
            <span>{user?.name || 'User'}</span>
          </div>
          <Button
            variant="default"
            className="bg-blue-500 hover:bg-blue-600 text-white"
            onClick={handleLogout}
          >
            Log Out
          </Button>
        </div>
      </header>

      <main className="p-8">
        <h1 className="text-2xl font-semibold mb-8">Total Number of Attacks Detected</h1>

        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        )}
        
        <div className="grid grid-cols-3 gap-8 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-50 p-8 rounded-lg text-center">
              <div className="text-4xl font-bold mb-2">{stat.count}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">My Domain</h2>
          <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
            <Dialog.Trigger asChild>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">Add New Domain</Button>
            </Dialog.Trigger>
            <Dialog.Portal>
              <Dialog.Overlay className="fixed inset-0 bg-black/50" />
              <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[400px]">
                <div className="flex justify-between items-center mb-4">
                  <Dialog.Title className="text-xl font-semibold">Add New Domain</Dialog.Title>
                  <Dialog.Close className="text-gray-400 hover:text-gray-600">
                    <span className="text-xl">×</span>
                  </Dialog.Close>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Domain Link
                  </label>
                  <input
                    type="text"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    placeholder="E.g. trenteknologimobile.com"
                    className="w-full px-3 py-2 border rounded-md"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={handleAddDomain}
                  >
                    Save
                  </Button>
                </div>
              </Dialog.Content>
            </Dialog.Portal>
          </Dialog.Root>
        </div>

        <div className="grid grid-cols-3 gap-8">
          {domains.map((domain, index) => (
            <div
              key={index}
              className="bg-gray-50 p-6 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => navigate(`/domain/${domain.domain}`)}
            >
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                  S
                </div>
                <span className="font-medium">{domain.domain}</span>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Security Log:</h3>
                {domain.hasAlert && (
                  <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-4">
                    <div className="flex items-center gap-2 text-red-600 mb-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="font-medium">SQL Injection Detected</span>
                    </div>
                    <p className="text-sm text-gray-600">{domain.alertMessage}</p>
                  </div>
                )}
                <div className="space-y-2">
                  {domain.logs.map((log, logIndex) => (
                    <div key={logIndex} className="text-sm text-gray-600">
                      {log}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </main>

      <footer className="text-center py-6 text-gray-600 border-t">
        Copyright 2025 I Nyoman Darmayoga - PUI
      </footer>
    </div>
  );
};