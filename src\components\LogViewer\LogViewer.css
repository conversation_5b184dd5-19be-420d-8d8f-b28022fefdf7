.log-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.log-viewer {
  background: white;
  border-radius: 8px;
  width: 90vw;
  height: 80vh;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.log-viewer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.log-viewer-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.log-viewer-summary {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.log-viewer-summary span {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #e9ecef;
  color: #495057;
}

.error-count {
  background-color: #f8d7da !important;
  color: #721c24 !important;
}

.fatal-count {
  background-color: #d1ecf1 !important;
  color: #0c5460 !important;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-button:hover {
  background-color: #e9ecef;
  color: #333;
}

.log-viewer-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.filter-group label {
  font-weight: 500;
  color: #495057;
  white-space: nowrap;
}

.filter-group select,
.filter-group input[type="text"] {
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.filter-group input[type="checkbox"] {
  margin-right: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.action-buttons button {
  padding: 6px 12px;
  border: 1px solid #007bff;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-buttons button:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.action-buttons button.danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.action-buttons button.danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.log-viewer-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #fff;
}

.no-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  font-style: italic;
}

.log-entry {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 20px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.log-entry:hover {
  background-color: #f8f9fa;
}

.log-entry.log-debug {
  border-left: 4px solid #6c757d;
}

.log-entry.log-info {
  border-left: 4px solid #007bff;
}

.log-entry.log-warn {
  border-left: 4px solid #ffc107;
  background-color: #fff3cd;
}

.log-entry.log-error {
  border-left: 4px solid #dc3545;
  background-color: #f8d7da;
}

.log-entry.log-fatal {
  border-left: 4px solid #6f42c1;
  background-color: #e2e3f3;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.log-timestamp {
  color: #6c757d;
  font-weight: 500;
  min-width: 80px;
}

.log-level {
  font-weight: bold;
  text-transform: uppercase;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.1);
  min-width: 50px;
  text-align: center;
}

.log-context {
  color: #495057;
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.log-user {
  color: #007bff;
  font-size: 11px;
}

.log-message {
  color: #333;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.log-error {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  margin: 8px 0;
}

.log-error strong {
  color: #dc3545;
}

.log-error details {
  margin-top: 8px;
}

.log-error summary {
  cursor: pointer;
  color: #007bff;
  font-weight: 500;
}

.log-error pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  overflow-x: auto;
  font-size: 11px;
  line-height: 1.3;
}

.log-metadata {
  margin: 8px 0;
}

.log-metadata summary {
  cursor: pointer;
  color: #6c757d;
  font-weight: 500;
  font-size: 12px;
}

.log-metadata pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  margin-top: 4px;
  overflow-x: auto;
  font-size: 11px;
  line-height: 1.3;
}

.log-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
  font-size: 11px;
  color: #6c757d;
}

.log-session {
  font-family: monospace;
}

.log-url {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive design */
@media (max-width: 768px) {
  .log-viewer-overlay {
    padding: 10px;
  }
  
  .log-viewer {
    width: 95vw;
    height: 90vh;
  }
  
  .log-viewer-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .action-buttons {
    margin-left: 0;
    justify-content: center;
  }
  
  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .log-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Scrollbar styling */
.log-viewer-content::-webkit-scrollbar {
  width: 8px;
}

.log-viewer-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.log-viewer-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.log-viewer-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}