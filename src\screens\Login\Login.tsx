import React, { useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { authService } from "../../services/auth";

export const Login = (): JSX.Element => {
  const navigate = useNavigate();
  const googleButtonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Check if user is already authenticated
    if (authService.isAuthenticated()) {
      navigate('/dashboard');
      return;
    }

    // Initialize Google Sign-In when component mounts
    const initializeGoogleSignIn = () => {
      if (window.google && googleButtonRef.current) {
        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: handleCredentialResponse,
        });

        window.google.accounts.id.renderButton(
          googleButtonRef.current,
          {
            theme: 'outline',
            size: 'large',
            width: '100%',
            text: 'signin_with',
            shape: 'rectangular',
          }
        );
      }
    };

    // Wait for Google script to load
    if (window.google) {
      initializeGoogleSignIn();
    } else {
      const checkGoogle = setInterval(() => {
        if (window.google) {
          clearInterval(checkGoogle);
          initializeGoogleSignIn();
        }
      }, 100);

      return () => clearInterval(checkGoogle);
    }
  }, [navigate]);

  const handleCredentialResponse = async (response: any) => {
    try {
      const result = await authService.verifyGoogleToken(response.credential);
      if (result.success && result.token) {
        localStorage.setItem('authToken', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        navigate('/dashboard');
      } else {
        alert('Login failed. Please try again.');
      }
    } catch (error) {
      console.error('Authentication error:', error);
      alert('Login failed. Please try again.');
    }
  };

  const handleGoogleSignIn = () => {
    if (window.google) {
      window.google.accounts.id.prompt();
    }
  };

  return (
    <main className="flex justify-center items-center min-h-screen bg-white">
      <Card className="border border-[#f4f4f4] p-10 w-auto">
        <CardContent className="flex flex-col items-center gap-16 p-0">
          <div className="flex flex-col gap-3 items-center text-center">
            <div className="flex justify-center gap-2 items-center">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C8.5 2 6 4.5 6 8c0 2.5 1.5 4.5 3.5 5.5L8 16c-1 1-1 2.5 0 3.5s2.5 1 3.5 0L13 18c1.5-1 2.5-2.5 2.5-4.5V8c0-3.5-2.5-6-6-6zm0 2c2.5 0 4 1.5 4 4v5.5c0 1.5-1 2.5-2 3l-1.5 1.5c-.5.5-1.5.5-2 0s-.5-1.5 0-2L12 14c-1.5-1-2.5-2.5-2.5-4.5C9.5 6.5 10.5 4 12 4z"/>
                </svg>
              </div>
              <h1 className="font-['Poppins',Helvetica] font-semibold text-black text-[32px] leading-8">
                SYRA
              </h1>
            </div>
            <p className="font-['Poppins',Helvetica] font-normal text-black text-base leading-5">
              Secure Your Realm Always
            </p>
          </div>

          <div className="w-full">
            <div ref={googleButtonRef} className="w-full"></div>
            <Button
              variant="outline"
              className="h-10 flex items-center gap-2.5 px-3 py-0 border-[#747775] w-full hover:bg-gray-50 mt-2"
              onClick={handleGoogleSignIn}
            >
              <img
                className="w-[18px] h-[18px]"
                alt="Google icon"
                src="/icon---google.svg"
              />
              <span className="font-['Poppins',Helvetica] font-normal text-[#1f1f1f] text-sm leading-5">
                Sign in with Google
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </main>
  );
};
