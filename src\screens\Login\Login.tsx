import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';
import { authService } from "../../services/auth";
import { logger } from "../../services/logger";

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;

export const Login = (): JSX.Element => {
  const navigate = useNavigate();

  useEffect(() => {
    logger.logComponent('Login', 'mounted');

    // Check if user is already authenticated
    if (authService.isAuthenticated()) {
      logger.info('Already authenticated user redirected to dashboard', 'login');
      navigate('/dashboard');
      return;
    }

    logger.info('Login page loaded for unauthenticated user', 'login');

    return () => {
      logger.logComponent('Login', 'unmounted');
    };
  }, [navigate]);

  const handleGoogleSuccess = (credentialResponse: any) => {
    logger.info('Google login successful', 'login');

    try {
      // Decode JWT token to get user info
      const token = credentialResponse.credential;
      const payload = JSON.parse(atob(token.split('.')[1]));

      const user = {
        id: payload.sub,
        name: payload.name,
        email: payload.email,
        picture: payload.picture
      };

      authService.login(user);
      logger.info('User authenticated successfully', 'login', { email: user.email });
      navigate('/dashboard');
    } catch (error) {
      logger.error('Error processing Google login', 'login', error as Error);
    }
  };

  const handleGoogleError = () => {
    logger.error('Google login failed', 'login');
    alert('Google login failed. Please try again.');
  };





  return (
    <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
      <main className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="w-full max-w-sm mx-auto px-4">
          {/* Logo dan Title - sesuai desain yang Anda berikan */}
          <div className="text-center mb-12">
            <div className="flex justify-center items-center gap-3 mb-4">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-2xl">S</span>
              </div>
              <h1 className="font-semibold text-black text-3xl">
                SYRA
              </h1>
            </div>
            <p className="font-normal text-gray-600 text-base">
              Secure Your Realm Always
            </p>
          </div>

          {/* Google Sign-In Button Container */}
          <div className="w-full flex justify-center">
            <GoogleLogin
              onSuccess={handleGoogleSuccess}
              onError={handleGoogleError}
              theme="outline"
              size="large"
              text="signin_with"
              shape="rectangular"
            />
          </div>
        </div>
      </main>
    </GoogleOAuthProvider>
  );
};
