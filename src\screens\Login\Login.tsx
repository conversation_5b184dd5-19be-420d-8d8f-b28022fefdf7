import React, { useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "../../components/ui/card";
import { authService } from "../../services/auth";
import { logger } from "../../services/logger";

export const Login = (): JSX.Element => {
  const navigate = useNavigate();
  const googleButtonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    logger.logComponent('Login', 'mounted');
    
    // Check if user is already authenticated
    if (authService.isAuthenticated()) {
      logger.info('Already authenticated user redirected to dashboard', 'login');
      logger.logNavigation('/login', '/dashboard');
      navigate('/dashboard');
      return;
    }

    logger.info('Login page loaded for unauthenticated user', 'login');

    // Initialize Google Sign-In when component mounts
    const initializeGoogleSignIn = () => {
      if (window.google && googleButtonRef.current) {
        logger.debug('Google Sign-In script loaded successfully', 'login');
        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: handleCredentialResponse,
        });

        window.google.accounts.id.renderButton(
          googleButtonRef.current,
          {
            theme: 'outline',
            size: 'large',
            width: '100%',
            text: 'signin_with',
            shape: 'rectangular',
          }
        );
      }
    };

    // Wait for Google script to load
    logger.debug('Loading Google Sign-In script', 'login');
    if (window.google) {
      initializeGoogleSignIn();
    } else {
      const checkGoogle = setInterval(() => {
        if (window.google) {
          clearInterval(checkGoogle);
          initializeGoogleSignIn();
        }
      }, 100);

      return () => {
        logger.logComponent('Login', 'unmounted');
        clearInterval(checkGoogle);
      };
    }
  }, [navigate]);

  const handleCredentialResponse = async (response: any) => {
    try {
      const result = await authService.verifyGoogleToken(response.credential);
      if (result.success && result.token) {
        localStorage.setItem('authToken', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        navigate('/dashboard');
      } else {
        alert('Login failed. Please try again.');
      }
    } catch (error) {
      console.error('Authentication error:', error);
      alert('Login failed. Please try again.');
    }
  };





  return (
    <main className="flex justify-center items-center min-h-screen bg-white">
      <Card className="border border-[#f4f4f4] p-10 w-auto">
        <CardContent className="flex flex-col items-center gap-16 p-0">
          <div className="flex flex-col gap-3 items-center text-center">
            <div className="flex justify-center gap-2 items-center">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <h1 className="font-['Poppins',Helvetica] font-semibold text-black text-[32px] leading-8">
                SYRA
              </h1>
            </div>
            <p className="font-['Poppins',Helvetica] font-normal text-black text-base leading-5">
              Secure Your Realm Always
            </p>
          </div>

          <div className="w-full">
            <div ref={googleButtonRef} className="w-full flex justify-center"></div>
          </div>
        </CardContent>
      </Card>
    </main>
  );
};
