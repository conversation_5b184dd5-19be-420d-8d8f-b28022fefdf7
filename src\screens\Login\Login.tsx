import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';
import { authService } from "../../services/auth";
import { logger } from "../../services/logger";

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;

export const Login = (): JSX.Element => {
  const navigate = useNavigate();

  useEffect(() => {
    logger.logComponent('Login', 'mounted');

    // Check if user is already authenticated
    if (authService.isAuthenticated()) {
      logger.info('Already authenticated user redirected to dashboard', 'login');
      navigate('/dashboard');
      return;
    }

    logger.info('Login page loaded for unauthenticated user', 'login');

    return () => {
      logger.logComponent('Login', 'unmounted');
    };
  }, [navigate]);

  const handleGoogleSuccess = (credentialResponse: any) => {
    logger.info('Google login successful', 'login');

    try {
      // Decode JWT token to get user info
      const token = credentialResponse.credential;
      const payload = JSON.parse(atob(token.split('.')[1]));

      const user = {
        id: payload.sub,
        name: payload.name,
        email: payload.email,
        picture: payload.picture
      };

      authService.login(user);
      logger.info('User authenticated successfully', 'login', { email: user.email });
      navigate('/dashboard');
    } catch (error) {
      logger.error('Error processing Google login', 'login', error as Error);
    }
  };

  const handleGoogleError = () => {
    logger.error('Google login failed', 'login');
  };

  const handleDemoLogin = () => {
    logger.info('Demo login initiated', 'login');

    const demoUser = {
      id: '1',
      name: 'I Nyoman Darmayoga',
      email: '<EMAIL>'
    };

    authService.login(demoUser);
    logger.info('Demo login successful', 'login');
    navigate('/dashboard');
  };





  return (
    <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
      <main className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="w-full max-w-sm mx-auto px-4">
          {/* Logo dan Title - sesuai desain yang Anda berikan */}
          <div className="text-center mb-12">
            <div className="flex justify-center items-center gap-3 mb-4">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-2xl">S</span>
              </div>
              <h1 className="font-semibold text-black text-3xl">
                SYRA
              </h1>
            </div>
            <p className="font-normal text-gray-600 text-base">
              Secure Your Realm Always
            </p>
          </div>

          {/* Google Sign-In Button Container */}
          <div className="w-full space-y-4">
            {/* Official Google Sign-In Button */}
            <div className="w-full flex justify-center">
              <GoogleLogin
                onSuccess={handleGoogleSuccess}
                onError={handleGoogleError}
                theme="outline"
                size="large"
                width="100%"
                text="signin_with"
                shape="rectangular"
              />
            </div>

            {/* Demo Button untuk testing - sesuai desain */}
            <button
              onClick={handleDemoLogin}
              className="w-full bg-white border border-gray-300 rounded-md px-4 py-3 flex items-center justify-center gap-3 hover:bg-gray-50 transition-colors shadow-sm"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
              </svg>
              <span className="text-gray-700 font-medium">Sign in with Google</span>
            </button>

            <p className="text-xs text-gray-500 text-center">
              Demo mode - klik tombol di atas untuk masuk
            </p>
          </div>
        </div>
      </main>
    </GoogleOAuthProvider>
  );
};
