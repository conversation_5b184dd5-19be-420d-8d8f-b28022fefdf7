interface User {
  id: number;
  email: string;
  name: string;
  picture?: string;
}

interface AuthResponse {
  success: boolean;
  token?: string;
  user?: User;
  message?: string;
}

class AuthService {
  private apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
  private clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;

  constructor() {
    this.initializeGoogleSignIn();
  }

  private initializeGoogleSignIn() {
    if (typeof window !== 'undefined' && window.google) {
      window.google.accounts.id.initialize({
        client_id: this.clientId,
        callback: this.handleCredentialResponse.bind(this),
      });
    }
  }

  private async handleCredentialResponse(response: any) {
    try {
      const result = await this.verifyGoogleToken(response.credential);
      if (result.success && result.token) {
        localStorage.setItem('authToken', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        window.location.href = '/dashboard';
      }
    } catch (error) {
      console.error('Authentication error:', error);
    }
  }

  async verifyGoogleToken(token: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/api/auth/google`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Token verification error:', error);
      return { success: false, message: 'Authentication failed' };
    }
  }

  signInWithGoogle() {
    if (typeof window !== 'undefined' && window.google) {
      window.google.accounts.id.prompt();
    }
  }

  renderGoogleSignInButton(elementId: string) {
    if (typeof window !== 'undefined' && window.google) {
      window.google.accounts.id.renderButton(
        document.getElementById(elementId),
        {
          theme: 'outline',
          size: 'large',
          width: '100%',
          text: 'signin_with',
        }
      );
    }
  }

  getToken(): string | null {
    return localStorage.getItem('authToken');
  }

  getUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    window.location.href = '/';
  }

  async makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
    const token = this.getToken();
    
    const headers = {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(`${this.apiUrl}${url}`, {
      ...options,
      headers,
    });

    if (response.status === 401) {
      this.logout();
      throw new Error('Authentication required');
    }

    return response;
  }
}

// Global type declarations
declare global {
  interface Window {
    google: any;
  }
}

export const authService = new AuthService();
export type { User, AuthResponse };
