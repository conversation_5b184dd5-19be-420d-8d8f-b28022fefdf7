import { logger } from './logger';

interface User {
  id: number;
  email: string;
  name: string;
  picture?: string;
}

interface AuthResponse {
  success: boolean;
  token?: string;
  user?: User;
  message?: string;
}

class AuthService {
  private apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
  private clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;

  constructor() {
    this.initializeGoogleSignIn();
  }

  private initializeGoogleSignIn() {
    if (typeof window !== 'undefined' && window.google) {
      window.google.accounts.id.initialize({
        client_id: this.clientId,
        callback: this.handleCredentialResponse.bind(this),
      });
    }
  }

  private async handleCredentialResponse(response: any) {
    try {
      logger.info('Google credential response received', 'auth');
      const result = await this.verifyGoogleToken(response.credential);
      
      if (result.success && result.token) {
        localStorage.setItem('authToken', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
        
        logger.logAuth('google_signin', true, undefined, {
          userId: result.user?.id,
          userEmail: result.user?.email,
          userName: result.user?.name
        });
        
        logger.logNavigation(window.location.pathname, '/dashboard');
        window.location.href = '/dashboard';
      } else {
        logger.logAuth('google_signin', false, undefined, {
          message: result.message,
          response: result
        });
      }
    } catch (error) {
      logger.logAuth('google_signin', false, error as Error, {
        step: 'credential_response_handling'
      });
    }
  }

  async verifyGoogleToken(token: string): Promise<AuthResponse> {
    const startTime = performance.now();
    try {
      logger.debug('Verifying Google token', 'auth');
      
      const response = await fetch(`${this.apiUrl}/api/auth/google`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();
      const duration = performance.now() - startTime;
      
      logger.logApiCall('POST', '/api/auth/google', response.status, duration);
      logger.logPerformance('verifyGoogleToken', duration);
      
      if (data.success) {
        logger.logAuth('token_verification', true, undefined, {
          userId: data.user?.id,
          duration
        });
      } else {
        logger.logAuth('token_verification', false, undefined, {
          message: data.message,
          duration
        });
      }
      
      return data;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.logAuth('token_verification', false, error as Error, {
        duration,
        step: 'api_call'
      });
      logger.logApiCall('POST', '/api/auth/google', undefined, duration, error as Error);
      return { success: false, message: 'Authentication failed' };
    }
  }

  signInWithGoogle() {
    try {
      logger.info('Initiating Google sign-in', 'auth');
      if (typeof window !== 'undefined' && window.google) {
        window.google.accounts.id.prompt();
        logger.debug('Google sign-in prompt displayed', 'auth');
      } else {
        logger.warn('Google sign-in not available - window.google not found', 'auth');
      }
    } catch (error) {
      logger.error('Error initiating Google sign-in', 'auth', error as Error);
    }
  }

  renderGoogleSignInButton(elementId: string) {
    if (typeof window !== 'undefined' && window.google) {
      window.google.accounts.id.renderButton(
        document.getElementById(elementId),
        {
          theme: 'outline',
          size: 'large',
          width: '100%',
          text: 'signin_with',
        }
      );
    }
  }

  getToken(): string | null {
    return localStorage.getItem('authToken');
  }

  getUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  login(user: User) {
    try {
      logger.info('User logging in', 'auth', {
        userId: user.id,
        userEmail: user.email
      });

      const token = 'demo-jwt-token-' + Date.now();
      localStorage.setItem('authToken', token);
      localStorage.setItem('user', JSON.stringify(user));

      logger.logAuth('demo_login', true, undefined, {
        userId: user.id,
        userEmail: user.email,
        userName: user.name
      });
    } catch (error) {
      logger.error('Error during login', 'auth', error as Error);
    }
  }

  logout() {
    try {
      const user = this.getUser();
      logger.info('User logging out', 'auth', {
        userId: user?.id,
        userEmail: user?.email
      });
      
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      
      logger.logAuth('logout', true, undefined, {
        userId: user?.id,
        redirectTo: '/'
      });
      
      logger.logNavigation(window.location.pathname, '/');
      window.location.href = '/';
    } catch (error) {
      logger.error('Error during logout', 'auth', error as Error);
      // Still try to clear storage and redirect
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/';
    }
  }

  async makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
    const startTime = performance.now();
    const token = this.getToken();
    
    if (!token) {
      logger.warn('No auth token available for authenticated request', 'auth', { url });
    }
    
    const headers = {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    try {
      const response = await fetch(`${this.apiUrl}${url}`, {
        ...options,
        headers,
      });
      
      const duration = performance.now() - startTime;
      
      if (response.status === 401) {
        logger.warn('Authentication required - token expired or invalid', 'auth', {
          url,
          status: response.status,
          duration
        });
        
        logger.logAuth('token_expired', false, undefined, {
          url,
          status: response.status
        });
        
        this.logout();
        throw new Error('Authentication required');
      }
      
      if (response.status >= 400) {
        logger.warn(`Authenticated request failed with status ${response.status}`, 'auth', {
          url,
          status: response.status,
          duration
        });
      }
      
      return response;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error('Authenticated request failed', 'auth', error as Error, {
        url,
        duration,
        hasToken: !!token
      });
      throw error;
    }
  }
}

// Global type declarations
declare global {
  interface Window {
    google: any;
  }
}

export const authService = new AuthService();
export type { User, AuthResponse };
