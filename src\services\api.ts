import { authService } from './auth';

export interface Domain {
  id: number;
  user_id: number;
  domain_name: string;
  created_at: string;
  updated_at: string;
}

export interface SecurityLog {
  id: number;
  domain_id: number;
  log_message: string;
  log_type: string;
  created_at: string;
}

export interface Attack {
  id: number;
  domain_id: number;
  attack_type: string;
  status: string;
  severity: string;
  detected_at: string;
  resolved_at?: string;
  details?: string;
}

class ApiService {
  async getDomains(): Promise<Domain[]> {
    try {
      const response = await authService.makeAuthenticatedRequest('/api/domains');
      const data = await response.json();
      return data.success ? data.domains : [];
    } catch (error) {
      console.error('Error fetching domains:', error);
      return [];
    }
  }

  async addDomain(domainName: string): Promise<Domain | null> {
    try {
      const response = await authService.makeAuthenticatedRequest('/api/domains', {
        method: 'POST',
        body: JSON.stringify({ domainName }),
      });
      const data = await response.json();
      return data.success ? data.domain : null;
    } catch (error) {
      console.error('Error adding domain:', error);
      return null;
    }
  }

  async getSecurityLogs(domainId: number): Promise<SecurityLog[]> {
    try {
      const response = await authService.makeAuthenticatedRequest(`/api/domains/${domainId}/logs`);
      const data = await response.json();
      return data.success ? data.logs : [];
    } catch (error) {
      console.error('Error fetching security logs:', error);
      return [];
    }
  }

  async getAttacks(domainId: number): Promise<Attack[]> {
    try {
      const response = await authService.makeAuthenticatedRequest(`/api/domains/${domainId}/attacks`);
      const data = await response.json();
      return data.success ? data.attacks : [];
    } catch (error) {
      console.error('Error fetching attacks:', error);
      return [];
    }
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3001'}/api/health`);
      const data = await response.json();
      return data.status === 'OK';
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }
}

export const apiService = new ApiService();
