import { authService } from './auth';
import { logger } from './logger';

export interface Domain {
  id: number;
  name: string;
  created_at: string;
  last_updated: string;
}

export interface SecurityLog {
  id: number;
  domain_id: number;
  timestamp: string;
  ip_address: string;
  user_agent: string;
  request_method: string;
  request_path: string;
  response_status: number;
  response_time: number;
}

export interface Attack {
  id: number;
  domain_id: number;
  timestamp: string;
  attack_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source_ip: string;
  description: string;
  blocked: boolean;
}

class ApiService {
  async getDomains(): Promise<Domain[]> {
    const startTime = performance.now();
    try {
      logger.debug('Fetching domains', 'api');
      const response = await authService.makeAuthenticatedRequest('/api/domains');
      const data = await response.json();
      const duration = performance.now() - startTime;
      
      logger.logApiCall('GET', '/api/domains', response.status, duration);
      logger.logPerformance('getDomains', duration);
      
      if (data.success) {
        logger.info(`Successfully fetched ${data.domains.length} domains`, 'api');
        return data.domains;
      } else {
        logger.warn('API returned success=false for getDomains', 'api', { data });
        return [];
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error('Error fetching domains', 'api', error as Error, { duration });
      logger.logApiCall('GET', '/api/domains', undefined, duration, error as Error);
      return [];
    }
  }

  async addDomain(domain: string): Promise<Domain | null> {
    const startTime = performance.now();
    try {
      logger.info(`Adding new domain: ${domain}`, 'api');
      const response = await authService.makeAuthenticatedRequest('/api/domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain }),
      });
      const data = await response.json();
      const duration = performance.now() - startTime;
      
      logger.logApiCall('POST', '/api/domains', response.status, duration);
      logger.logPerformance('addDomain', duration);
      
      if (data.success) {
        logger.info(`Successfully added domain: ${domain}`, 'api', { domainId: data.domain.id });
        return data.domain;
      } else {
        logger.warn(`Failed to add domain: ${domain}`, 'api', { data, domain });
        return null;
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error adding domain: ${domain}`, 'api', error as Error, { domain, duration });
      logger.logApiCall('POST', '/api/domains', undefined, duration, error as Error);
      return null;
    }
  }

  async getSecurityLogs(domainId: number): Promise<SecurityLog[]> {
    const startTime = performance.now();
    try {
      logger.debug(`Fetching security logs for domain ${domainId}`, 'api');
      const response = await authService.makeAuthenticatedRequest(`/api/domains/${domainId}/logs`);
      const data = await response.json();
      const duration = performance.now() - startTime;
      
      logger.logApiCall('GET', `/api/domains/${domainId}/logs`, response.status, duration);
      logger.logPerformance('getSecurityLogs', duration);
      
      if (data.success) {
        logger.info(`Successfully fetched ${data.logs.length} security logs for domain ${domainId}`, 'api');
        return data.logs;
      } else {
        logger.warn(`API returned success=false for getSecurityLogs`, 'api', { domainId, data });
        return [];
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error fetching security logs for domain ${domainId}`, 'api', error as Error, { domainId, duration });
      logger.logApiCall('GET', `/api/domains/${domainId}/logs`, undefined, duration, error as Error);
      return [];
    }
  }

  async getAttacks(domainId: number): Promise<Attack[]> {
    const startTime = performance.now();
    try {
      logger.debug(`Fetching attacks for domain ${domainId}`, 'api');
      const response = await authService.makeAuthenticatedRequest(`/api/domains/${domainId}/attacks`);
      const data = await response.json();
      const duration = performance.now() - startTime;
      
      logger.logApiCall('GET', `/api/domains/${domainId}/attacks`, response.status, duration);
      logger.logPerformance('getAttacks', duration);
      
      if (data.success) {
        logger.info(`Successfully fetched ${data.attacks.length} attacks for domain ${domainId}`, 'api');
        return data.attacks;
      } else {
        logger.warn(`API returned success=false for getAttacks`, 'api', { domainId, data });
        return [];
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error fetching attacks for domain ${domainId}`, 'api', error as Error, { domainId, duration });
      logger.logApiCall('GET', `/api/domains/${domainId}/attacks`, undefined, duration, error as Error);
      return [];
    }
  }

  async checkHealth(): Promise<boolean> {
    const startTime = performance.now();
    try {
      logger.debug('Performing health check', 'api');
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3001'}/api/health`);
      const data = await response.json();
      const duration = performance.now() - startTime;
      
      logger.logApiCall('GET', '/api/health', response.status, duration);
      logger.logPerformance('checkHealth', duration);
      
      const isHealthy = data.status === 'OK';
      if (isHealthy) {
        logger.info('Health check passed', 'api');
      } else {
        logger.warn('Health check failed - API not healthy', 'api', { data });
      }
      
      return isHealthy;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error('Health check failed with error', 'api', error as Error, { duration });
      logger.logApiCall('GET', '/api/health', undefined, duration, error as Error);
      return false;
    }
  }
}

export const apiService = new ApiService();
