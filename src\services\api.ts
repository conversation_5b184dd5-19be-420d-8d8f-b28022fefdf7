import { logger } from './logger';

export interface Domain {
  id: number;
  domain_name: string;
  created_at: string;
  updated_at?: string;
  user_id?: number;
  hasAlert?: boolean;
}

export interface SecurityLog {
  id: number;
  domain_id: number;
  log_message: string;
  log_type: string;
  created_at: string;
}

export interface Attack {
  id: number;
  domain_id: number;
  attack_type: string;
  status: string;
  severity: string;
  detected_at: string;
  resolved_at?: string;
  details: string;
}

class ApiService {
  async initialize(): Promise<void> {
    logger.info('API Service initialized in demo mode', 'api');
  }

  async getDomains(): Promise<Domain[]> {
    const startTime = performance.now();
    try {
      logger.debug('Fetching domains', 'api');

      // Demo data sesuai dengan desain yang diberikan
      const mockDomains: Domain[] = [
        {
          id: 1,
          domain_name: 'trenteknologimobile.com',
          created_at: '2025-05-01T10:22:45Z',
          hasAlert: true
        },
        {
          id: 2,
          domain_name: 'proyekutamainformatika.com',
          created_at: '2025-05-01T10:22:45Z',
          hasAlert: false
        },
        {
          id: 3,
          domain_name: 'websiteku.org',
          created_at: '2025-05-01T10:22:45Z',
          hasAlert: false
        }
      ];

      const duration = performance.now() - startTime;
      logger.logPerformance('getDomains', duration);
      logger.info(`Successfully fetched ${mockDomains.length} domains`, 'api');

      return mockDomains;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error('Error fetching domains', 'api', error as Error, { duration });
      return [];
    }
  }

  async addDomain(domain: string): Promise<Domain | null> {
    const startTime = performance.now();
    try {
      logger.info(`Adding new domain: ${domain}`, 'api');

      // Demo implementation - in real app, this would save to database
      const newDomain: Domain = {
        id: Date.now(),
        domain_name: domain,
        created_at: new Date().toISOString(),
        hasAlert: false
      };

      const duration = performance.now() - startTime;
      logger.logPerformance('addDomain', duration);
      logger.info(`Successfully added domain: ${domain}`, 'api', { domainId: newDomain.id });

      return newDomain;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error adding domain: ${domain}`, 'api', error as Error, { domain, duration });
      return null;
    }
  }

  async getSecurityLogs(domainId: number): Promise<SecurityLog[]> {
    const startTime = performance.now();
    try {
      logger.debug(`Fetching security logs for domain ${domainId}`, 'api');

      // Demo data sesuai dengan desain yang diberikan
      const mockLogs: SecurityLog[] = [
        {
          id: 1,
          domain_id: domainId,
          log_message: '[2025-05-1 10:22:47] No threats detected - normal activity from all sources',
          log_type: 'info',
          created_at: '2025-05-01T10:22:47Z'
        },
        {
          id: 2,
          domain_id: domainId,
          log_message: '[2025-05-1 10:22:46] No threats detected - normal activity from all sources',
          log_type: 'info',
          created_at: '2025-05-01T10:22:46Z'
        },
        {
          id: 3,
          domain_id: domainId,
          log_message: '[2025-05-1 10:22:43] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:43Z'
        }
      ];

      const duration = performance.now() - startTime;
      logger.logPerformance('getSecurityLogs', duration);
      logger.info(`Successfully fetched ${mockLogs.length} security logs for domain ${domainId}`, 'api');

      return mockLogs;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error fetching security logs for domain ${domainId}`, 'api', error as Error, { domainId, duration });
      return [];
    }
  }

  async getAttacks(domainId: number): Promise<Attack[]> {
    const startTime = performance.now();
    try {
      logger.debug(`Fetching attacks for domain ${domainId}`, 'api');

      // Demo data sesuai dengan desain yang diberikan
      const mockAttacks: Attack[] = [
        {
          id: 1,
          domain_id: domainId,
          attack_type: 'DDoS Detected',
          status: 'Resolved',
          severity: 'Medium',
          detected_at: '2025-05-1 10:22:45',
          resolved_at: '2025-05-1 10:23:30',
          details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack'
        },
        {
          id: 2,
          domain_id: domainId,
          attack_type: 'SQL Injection Detected',
          status: 'Resolved',
          severity: 'Medium',
          detected_at: '2025-05-1 10:22:45',
          resolved_at: '2025-05-1 10:23:30',
          details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack'
        },
        {
          id: 3,
          domain_id: domainId,
          attack_type: 'XSS Detected',
          status: 'Resolved',
          severity: 'Medium',
          detected_at: '2025-05-1 10:22:45',
          resolved_at: '2025-05-1 10:23:30',
          details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack'
        }
      ];

      const duration = performance.now() - startTime;
      logger.logPerformance('getAttacks', duration);
      logger.info(`Successfully fetched ${mockAttacks.length} attacks for domain ${domainId}`, 'api');

      return mockAttacks;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error fetching attacks for domain ${domainId}`, 'api', error as Error, { domainId, duration });
      return [];
    }
  }

  async checkHealth(): Promise<boolean> {
    const startTime = performance.now();
    try {
      logger.debug('Performing health check', 'api');

      // Demo implementation - always return healthy
      const duration = performance.now() - startTime;
      logger.logPerformance('checkHealth', duration);
      logger.info('Health check passed', 'api');

      return true;
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error('Health check failed with error', 'api', error as Error, { duration });
      return false;
    }
  }
}

export const apiService = new ApiService();
