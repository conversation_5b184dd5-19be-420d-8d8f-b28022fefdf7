import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { authService } from "../../services/auth";
import { logger } from "../../services/logger";

interface SecurityLog {
  id: number;
  log_message: string;
  log_type: string;
  created_at: string;
}

interface Attack {
  id: number;
  attack_type: string;
  status: string;
  severity: string;
  detected_at: string;
  resolved_at?: string;
  details: string;
}

export const DomainDetail = (): JSX.Element => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [domainName, setDomainName] = useState<string>('');
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([]);
  const [attacks, setAttacks] = useState<Attack[]>([]);
  const [loading, setLoading] = useState(true);
  const user = authService.getUser();

  useEffect(() => {
    logger.logComponent('DomainDetail', 'mounted');

    // Check authentication
    if (!authService.isAuthenticated()) {
      logger.warn('Unauthenticated user redirected from domain detail', 'domain-detail');
      navigate('/');
      return;
    }

    if (id) {
      const decodedDomainName = decodeURIComponent(id);
      setDomainName(decodedDomainName);
      logger.info(`Loading domain detail for: ${decodedDomainName}`, 'domain-detail');
      loadDomainData(decodedDomainName);
    } else {
      logger.warn('No domain name provided in URL', 'domain-detail');
    }

    return () => {
      logger.logComponent('DomainDetail', 'unmounted');
    };
  }, [navigate, id]);

  const loadDomainData = async (domain: string) => {
    const startTime = performance.now();
    try {
      setLoading(true);
      logger.debug(`Loading data for domain: ${domain}`, 'domain-detail');

      // Demo data sesuai dengan desain yang diberikan
      const mockLogs: SecurityLog[] = [
        {
          id: 1,
          log_message: '[2025-05-1 10:22:47] No threats detected - normal activity from all sources',
          log_type: 'info',
          created_at: '2025-05-01T10:22:47Z'
        },
        {
          id: 2,
          log_message: '[2025-05-1 10:22:46] No threats detected - normal activity from all sources',
          log_type: 'info',
          created_at: '2025-05-01T10:22:46Z'
        },
        {
          id: 3,
          log_message: '[2025-05-1 10:22:43] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:43Z'
        },
        {
          id: 4,
          log_message: '[2025-05-1 10:22:42] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:42Z'
        },
        {
          id: 5,
          log_message: '[2025-05-1 10:22:41] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:41Z'
        },
        {
          id: 6,
          log_message: '[2025-05-1 10:22:40] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:40Z'
        },
        {
          id: 7,
          log_message: '[2025-05-1 10:22:39] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:39Z'
        },
        {
          id: 8,
          log_message: '[2025-05-1 10:22:38] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:38Z'
        },
        {
          id: 9,
          log_message: '[2025-05-1 10:22:37] Clean traffic observed - no suspicious patterns found',
          log_type: 'info',
          created_at: '2025-05-01T10:22:37Z'
        }
      ];

      const mockAttacks: Attack[] = [
        {
          id: 1,
          attack_type: 'DDoS Detected',
          status: 'Resolved',
          severity: 'Medium',
          detected_at: '2025-05-1 10:22:45',
          resolved_at: '2025-05-1 10:23:30',
          details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack'
        },
        {
          id: 2,
          attack_type: 'SQL Injection Detected',
          status: 'Resolved',
          severity: 'Medium',
          detected_at: '2025-05-1 10:22:45',
          resolved_at: '2025-05-1 10:23:30',
          details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack'
        },
        {
          id: 3,
          attack_type: 'XSS Detected',
          status: 'Resolved',
          severity: 'Medium',
          detected_at: '2025-05-1 10:22:45',
          resolved_at: '2025-05-1 10:23:30',
          details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack'
        }
      ];

      setSecurityLogs(mockLogs);
      setAttacks(mockAttacks);

      const duration = performance.now() - startTime;
      logger.logPerformance('loadDomainData', duration);
      logger.info(`Successfully loaded data for domain: ${domain}`, 'domain-detail', {
        logsCount: mockLogs.length,
        attacksCount: mockAttacks.length,
        duration
      });
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error loading domain data for: ${domain}`, 'domain-detail', error as Error, {
        duration
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logger.info('User initiated logout from domain detail', 'domain-detail', { domainName });
    authService.logout();
  };

  const handleBack = () => {
    logger.logNavigation(`/domain/${domainName}`, '/dashboard');
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white flex justify-between items-center px-8 py-4 border-b shadow-sm">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
            S
          </div>
          <span className="font-semibold text-xl">SYRA</span>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
              I
            </div>
            <span className="font-medium">I Nyoman Darmayoga</span>
          </div>
          <Button
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
            onClick={handleLogout}
          >
            Log Out
          </Button>
        </div>
      </header>

      <main className="p-8 max-w-4xl mx-auto">
        {/* Back Button */}
        <div className="mb-6">
          <button
            onClick={handleBack}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
          >
            <span>←</span>
            <span>Kembali</span>
          </button>
        </div>

        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        )}

        {!loading && (
          <>
            {/* Domain Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg mx-auto mb-4">
                S
              </div>
              <h1 className="text-2xl font-semibold mb-2">{domainName}</h1>
              <div className="text-gray-500 text-sm space-y-1">
                <div>Created At: 1 May 2025</div>
                <div>Last Update At: 2 May 2025</div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-3 gap-6 mb-8">
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <div className="text-3xl font-bold mb-2">0</div>
                <div className="text-gray-600">DDoS</div>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <div className="text-3xl font-bold mb-2">0</div>
                <div className="text-gray-600">SQL Injection</div>
              </div>
              <div className="bg-white rounded-lg p-6 text-center shadow-sm">
                <div className="text-3xl font-bold mb-2">0</div>
                <div className="text-gray-600">XSS</div>
              </div>
            </div>

            {/* Security Log */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">Security Log</h2>
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {securityLogs.map((log) => (
                    <div key={log.id} className="text-sm text-gray-600 py-1">
                      {log.log_message}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Detected Attacks */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Detected Attacks</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {attacks.map((attack) => (
                  <div key={attack.id} className="bg-white rounded-lg p-6 shadow-sm">
                    <div className="mb-4">
                      <h3 className="font-semibold text-lg mb-2">{attack.attack_type}</h3>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Mitigation Status</span>
                          <span className="text-green-600 font-medium">{attack.status}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Severity</span>
                          <span className="text-orange-600 font-medium">{attack.severity}</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-sm text-gray-600 mb-4 space-y-1">
                      <div>Detected At: {attack.detected_at}</div>
                      <div>Resolved At: {attack.resolved_at}</div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2 text-sm">Mitigation Details</h4>
                      <p className="text-xs text-gray-600 leading-relaxed">{attack.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </main>

      <footer className="text-center py-6 text-gray-600 border-t bg-white mt-8">
        Copyright 2025 I Nyoman Darmayoga - PUI
      </footer>
    </div>
  );

};