import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { authService } from "../../services/auth";
import { apiService, SecurityLog, Attack } from "../../services/api";
import { logger } from "../../services/logger";
import { LogViewer } from "../../components/LogViewer/LogViewer";

export const DomainDetail = (): JSX.Element => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [domainName, setDomainName] = useState<string>('');
  const [domainData, setDomainData] = useState<any>(null);
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([]);
  const [attacks, setAttacks] = useState<Attack[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(authService.getUser());
  const [showLogViewer, setShowLogViewer] = useState(false);

  useEffect(() => {
    logger.logComponent('DomainDetail', 'mounted', { domainName });
    
    // Check authentication
    if (!authService.isAuthenticated()) {
      logger.warn('Unauthenticated user redirected from domain detail', 'domain-detail', { domainName });
      navigate('/');
      return;
    }

    if (id) {
      const decodedDomainName = decodeURIComponent(id);
      setDomainName(decodedDomainName);
      logger.info(`Loading domain detail for: ${decodedDomainName}`, 'domain-detail');
      loadDomainData();
    } else {
      logger.warn('No domain name provided in URL', 'domain-detail');
    }
    
    return () => {
      logger.logComponent('DomainDetail', 'unmounted', { domainName });
    };
  }, [navigate, id]);

  const loadDomainData = async () => {
    if (!id) {
      logger.warn('loadDomainData called without domain id', 'domain-detail');
      return;
    }

    const startTime = performance.now();
    try {
      const decodedDomainName = decodeURIComponent(id);
      logger.debug(`Loading data for domain: ${decodedDomainName}`, 'domain-detail');
      setLoading(true);
      
      // Get all domains and find the matching one
      const domains = await apiService.getDomains();
      const currentDomain = domains.find(d => d.domain_name === decodedDomainName);
      
      if (currentDomain) {
        logger.info(`Found domain: ${decodedDomainName} (ID: ${currentDomain.id})`, 'domain-detail');
        setDomainData(currentDomain);
        
        logger.debug(`Loading logs and attacks for domain ${currentDomain.id}`, 'domain-detail');
        const [logsData, attacksData] = await Promise.all([
          apiService.getSecurityLogs(currentDomain.id),
          apiService.getAttacks(currentDomain.id)
        ]);

        setSecurityLogs(logsData);
        setAttacks(attacksData);
        
        const duration = performance.now() - startTime;
        logger.logPerformance('loadDomainData', duration);
        logger.info(`Successfully loaded domain data for ${decodedDomainName}`, 'domain-detail', {
          domainId: currentDomain.id,
          logsCount: logsData.length,
          attacksCount: attacksData.length,
          duration
        });
      } else {
        logger.error(`Domain not found: ${decodedDomainName}`, 'domain-detail', undefined, {
          domainName: decodedDomainName,
          availableDomains: domains.map(d => d.domain_name)
        });
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Error loading domain data for ${domainName}`, 'domain-detail', error as Error, {
        domainName,
        duration
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logger.info('User initiated logout from domain detail', 'domain-detail', { domainName });
    authService.logout();
    navigate('/');
  };

  const stats = [
    { label: 'Total Logs', count: securityLogs.length },
    { label: 'Attacks Detected', count: attacks.length },
    { label: 'Resolved Attacks', count: attacks.filter(a => a.status === 'resolved').length },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2 text-gray-600">Loading domain details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => navigate('/dashboard')}
                className="text-blue-600 hover:text-blue-800"
              >
                ← Back to Dashboard
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Domain Security Details</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">Welcome, {user?.name}</span>
              <Button
                onClick={() => setShowLogViewer(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                title="View System Logs"
              >
                📋 Logs
              </Button>
              <Button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md"
              >
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm text-center">
            <img
              src="/logo-1.png"
              alt="SYRA Logo"
              className="mx-auto mb-4 h-16 w-auto"
            />
            <h1 className="text-2xl font-semibold mb-2">{domainName}</h1>
            <div className="text-gray-600 text-center">
              {domainData ? (
                <>
                  <p>Created At: {new Date(domainData.created_at).toLocaleDateString()}</p>
                  <p>Last Update At: {new Date(domainData.updated_at || domainData.created_at).toLocaleDateString()}</p>
                </>
              ) : (
                <>
                  <p>Created At: Loading...</p>
                  <p>Last Update At: Loading...</p>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm text-center">
              <div className="text-4xl font-bold mb-2">{stat.count}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">Security Logs</h2>
            <div className="bg-white p-6 rounded-lg shadow-sm max-h-96 overflow-y-auto">
              {securityLogs.length > 0 ? (
                securityLogs.map((log, index) => (
                  <div key={log.id || index} className="text-sm text-gray-600 py-1 border-b border-gray-200 last:border-b-0">
                    [{new Date(log.timestamp).toLocaleString()}] {log.message}
                  </div>
                ))
              ) : (
                <div className="text-sm text-gray-500 text-center py-4">
                  No security logs available
                </div>
              )}
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4">Detected Attacks</h2>
            {attacks.length > 0 ? (
              <div className="grid grid-cols-1 gap-6">
                {attacks.map((attack, index) => (
                  <div key={attack.id || index} className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-lg mb-4">{attack.attack_type}</h3>

                    <div className="space-y-3">
                      <div>
                        <div className="text-sm text-gray-600">Mitigation Status</div>
                        <div className="font-medium">{attack.status}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-600">Severity</div>
                        <div className="font-medium">{attack.severity}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-600">Detected At</div>
                        <div className="font-medium">{new Date(attack.detected_at).toLocaleString()}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-600">Resolved At</div>
                        <div className="font-medium">{attack.resolved_at ? new Date(attack.resolved_at).toLocaleString() : 'Not resolved'}</div>
                      </div>

                      <div>
                        <div className="text-sm text-gray-600">Mitigation Details</div>
                        <div className="text-sm">{attack.details}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 text-lg mb-2">No attacks detected</div>
                <p className="text-gray-500">Your domain is secure and no threats have been detected.</p>
              </div>
            )}
          </div>
        </div>
      </main>

      <footer className="text-center py-6 text-gray-600 border-t">
        Copyright 2025 I Nyoman Darmayoga - PUI
      </footer>
      
      {showLogViewer && (
        <LogViewer onClose={() => setShowLogViewer(false)} />
      )}
    </div>
  );
};

export default DomainDetail;