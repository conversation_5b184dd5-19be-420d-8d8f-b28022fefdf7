import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { authService } from "../../services/auth";
import { apiService, SecurityLog, Attack } from "../../services/api";

interface Attack {
  type: string;
  status: string;
  severity: string;
  detectedAt: string;
  resolvedAt: string;
  details: string;
}

export const DomainDetail = (): JSX.Element => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [domainName, setDomainName] = useState<string>('');
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([]);
  const [attacks, setAttacks] = useState<Attack[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(authService.getUser());

  useEffect(() => {
    // Check authentication
    if (!authService.isAuthenticated()) {
      navigate('/');
      return;
    }

    if (id) {
      setDomainName(decodeURIComponent(id));
      loadDomainData();
    }
  }, [navigate, id]);

  const loadDomainData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      // For now, we'll use the domain name as ID since we're passing domain name in URL
      // In a real app, you'd pass the actual domain ID
      const domainId = 1; // This should be the actual domain ID from your database

      const [logsData, attacksData] = await Promise.all([
        apiService.getSecurityLogs(domainId),
        apiService.getAttacks(domainId)
      ]);

      setSecurityLogs(logsData);
      setAttacks(attacksData);
    } catch (error) {
      console.error('Error loading domain data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    authService.logout();
  };

  // Fallback data for demo purposes
  const fallbackAttacks: Attack[] = [
    {
      id: 1,
      domain_id: 1,
      attack_type: "DDoS Detected",
      status: "Resolved",
      severity: "Medium",
      detected_at: "2025-05-1 10:22:45",
      resolved_at: "2025-05-1 10:23:30",
      details: "The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack"
    },
    {
      id: 2,
      domain_id: 1,
      attack_type: "SQL Injection Detected",
      status: "Resolved",
      severity: "Medium",
      detected_at: "2025-05-1 10:22:45",
      resolved_at: "2025-05-1 10:23:30",
      details: "The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack"
    },
    {
      id: 3,
      domain_id: 1,
      attack_type: "XSS Detected",
      status: "Resolved",
      severity: "Medium",
      detected_at: "2025-05-1 10:22:45",
      resolved_at: "2025-05-1 10:23:30",
      details: "The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack"
    }
  ];

  // Use API data if available, otherwise use fallback
  const displayAttacks = attacks.length > 0 ? attacks : fallbackAttacks;

  const logs = [
    "[2025-05-1 10:22:47] No threats detected - normal activity from all sources",
    "[2025-05-1 10:22:46] No threats detected - normal activity from all sources",
    "[2025-05-1 10:22:43] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:43] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:42] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:42] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:42] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:41] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:41] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:41] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:40] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:40] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:39] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:38] Clean traffic observed - no suspicious patterns found",
    "[2025-05-1 10:22:38] Clean traffic observed - no suspicious patterns foud",
    "[2025-05-1 10:22:37] Clean traffic observed - no suspicious patterns found"
  ];

  const stats = [
    { label: "DDoS", count: 0 },
    { label: "SQL Injection", count: 0 },
    { label: "XSS", count: 0 }
  ];

  return (
    <div className="min-h-screen bg-white">
      <header className="flex justify-between items-center px-8 py-4 border-b">
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C8.5 2 6 4.5 6 8c0 2.5 1.5 4.5 3.5 5.5L8 16c-1 1-1 2.5 0 3.5s2.5 1 3.5 0L13 18c1.5-1 2.5-2.5 2.5-4.5V8c0-3.5-2.5-6-6-6zm0 2c2.5 0 4 1.5 4 4v5.5c0 1.5-1 2.5-2 3l-1.5 1.5c-.5.5-1.5.5-2 0s-.5-1.5 0-2L12 14c-1.5-1-2.5-2.5-2.5-4.5C9.5 6.5 10.5 4 12 4z"/>
            </svg>
          </div>
          <span className="font-['Poppins'] font-semibold text-2xl">SYRA</span>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
              I
            </div>
            <span>{user?.name || 'User'}</span>
          </div>
          <Button
            variant="default"
            className="bg-blue-500 hover:bg-blue-600 text-white"
            onClick={handleLogout}
          >
            Log Out
          </Button>
        </div>
      </header>

      <main className="p-8">
        <button
          onClick={() => navigate('/dashboard')}
          className="flex items-center gap-2 text-gray-600 mb-6"
        >
          ← Kembali
        </button>

        <div className="flex flex-col items-center mb-8">
          <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl mb-4 font-semibold">
            S
          </div>
          <h1 className="text-2xl font-semibold mb-2">{domainName}</h1>
          <div className="text-gray-600 text-center">
            <p>Created At: 1 May 2025</p>
            <p>Last Update At: 2 May 2025</p>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-8 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-50 p-8 rounded-lg text-center">
              <div className="text-4xl font-bold mb-2">{stat.count}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Security Log</h2>
          <div className="bg-gray-50 p-6 rounded-lg max-h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-sm text-gray-600 py-1 border-b border-gray-200 last:border-b-0">
                {log}
              </div>
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Detected Attacks</h2>
          <div className="grid grid-cols-3 gap-6">
            {displayAttacks.map((attack, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-lg">
                <h3 className="font-semibold text-lg mb-4">{attack.attack_type}</h3>

                <div className="space-y-3">
                  <div>
                    <div className="text-sm text-gray-600">Mitigation Status</div>
                    <div className="font-medium">{attack.status}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Severity</div>
                    <div className="font-medium">{attack.severity}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Detected At</div>
                    <div className="font-medium">{attack.detected_at}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Resolved At</div>
                    <div className="font-medium">{attack.resolved_at}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">Mitigation Details</div>
                    <div className="text-sm">{attack.details}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>

      <footer className="text-center py-6 text-gray-600 border-t">
        Copyright 2025 I Nyoman Darmayoga - PUI
      </footer>
    </div>
  );
};