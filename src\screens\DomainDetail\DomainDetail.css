.domain-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.back-btn:hover {
  background-color: #5a6268;
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  word-break: break-all;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logs-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
  transition: background-color 0.2s;
}

.logs-btn:hover {
  background-color: #0056b3;
}

.logout-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.logout-btn:hover {
  background-color: #c82333;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card .number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.stat-card.attacks .number {
  color: #dc3545;
}

.stat-card.logs .number {
  color: #007bff;
}

.stat-card.status .number {
  color: #28a745;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section h2 {
  margin: 0;
  padding: 24px;
  color: #333;
  font-size: 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.section-content {
  padding: 24px;
}

.log-entry,
.attack-entry {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 0;
}

.log-entry:last-child,
.attack-entry:last-child {
  border-bottom: none;
}

.log-entry h4,
.attack-entry h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.log-entry .timestamp,
.attack-entry .timestamp {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.log-entry .level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  margin-right: 8px;
}

.log-entry .level.info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.log-entry .level.warning {
  background-color: #fff3cd;
  color: #856404;
}

.log-entry .level.error {
  background-color: #f8d7da;
  color: #721c24;
}

.attack-entry .severity {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  margin-right: 8px;
}

.attack-entry .severity.high {
  background-color: #f8d7da;
  color: #721c24;
}

.attack-entry .severity.medium {
  background-color: #fff3cd;
  color: #856404;
}

.attack-entry .severity.low {
  background-color: #d4edda;
  color: #155724;
}

.attack-entry .type {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.attack-entry .source {
  color: #666;
  font-size: 14px;
  font-family: monospace;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
  font-size: 18px;
}

.footer {
  background-color: #333;
  color: white;
  text-align: center;
  padding: 20px;
  margin-top: 40px;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header-left {
    flex-direction: column;
    gap: 8px;
  }
  
  .user-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .header h1 {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 20px 10px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
}