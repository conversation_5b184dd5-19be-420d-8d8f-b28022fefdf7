import React, { useState, useEffect, useRef } from 'react';
import { logger, LogLevel, LogEntry } from '../../services/logger';
import './LogViewer.css';

interface LogViewerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const LogViewer: React.FC<LogViewerProps> = ({ isOpen, onClose }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [selectedLevel, setSelectedLevel] = useState<LogLevel | 'ALL'>('ALL');
  const [selectedContext, setSelectedContext] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [autoScroll, setAutoScroll] = useState(true);
  const [maxLogs, setMaxLogs] = useState(100);
  const logsEndRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Initial load
      loadLogs();
      
      // Set up auto-refresh
      intervalRef.current = setInterval(loadLogs, 1000);
    } else {
      // Clear interval when closed
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isOpen]);

  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [filteredLogs, autoScroll]);

  useEffect(() => {
    filterLogs();
  }, [logs, selectedLevel, selectedContext, searchTerm]);

  const loadLogs = () => {
    const allLogs = logger.getLogs(undefined, undefined, maxLogs);
    setLogs(allLogs);
  };

  const filterLogs = () => {
    let filtered = logs;

    // Filter by level
    if (selectedLevel !== 'ALL') {
      filtered = filtered.filter(log => log.level === selectedLevel);
    }

    // Filter by context
    if (selectedContext !== 'ALL') {
      filtered = filtered.filter(log => log.context === selectedContext);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(term) ||
        log.context?.toLowerCase().includes(term) ||
        log.error?.message.toLowerCase().includes(term)
      );
    }

    setFilteredLogs(filtered);
  };

  const getUniqueContexts = () => {
    const contexts = new Set(logs.map(log => log.context).filter(Boolean));
    return Array.from(contexts).sort();
  };

  const getLevelColor = (level: LogLevel) => {
    const colors = {
      [LogLevel.DEBUG]: '#6c757d',
      [LogLevel.INFO]: '#007bff',
      [LogLevel.WARN]: '#ffc107',
      [LogLevel.ERROR]: '#dc3545',
      [LogLevel.FATAL]: '#6f42c1'
    };
    return colors[level] || '#000';
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const exportLogs = () => {
    const dataStr = logger.exportLogs();
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `syra-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const clearLogs = () => {
    if (confirm('Are you sure you want to clear all logs?')) {
      logger.clearLogs();
      setLogs([]);
      setFilteredLogs([]);
    }
  };

  const getErrorSummary = () => {
    return logger.getErrorSummary();
  };

  if (!isOpen) return null;

  const summary = getErrorSummary();

  return (
    <div className="log-viewer-overlay">
      <div className="log-viewer">
        <div className="log-viewer-header">
          <h3>System Logs</h3>
          <div className="log-viewer-summary">
            <span>Total: {summary.total}</span>
            <span className="error-count">Errors: {summary.byLevel[LogLevel.ERROR]}</span>
            <span className="fatal-count">Fatal: {summary.byLevel[LogLevel.FATAL]}</span>
          </div>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="log-viewer-controls">
          <div className="filter-group">
            <label>Level:</label>
            <select 
              value={selectedLevel} 
              onChange={(e) => setSelectedLevel(e.target.value as LogLevel | 'ALL')}
            >
              <option value="ALL">All Levels</option>
              {Object.values(LogLevel).map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Context:</label>
            <select 
              value={selectedContext} 
              onChange={(e) => setSelectedContext(e.target.value)}
            >
              <option value="ALL">All Contexts</option>
              {getUniqueContexts().map(context => (
                <option key={context} value={context}>{context}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Search:</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search logs..."
            />
          </div>

          <div className="filter-group">
            <label>Max Logs:</label>
            <select 
              value={maxLogs} 
              onChange={(e) => setMaxLogs(Number(e.target.value))}
            >
              <option value={50}>50</option>
              <option value={100}>100</option>
              <option value={200}>200</option>
              <option value={500}>500</option>
            </select>
          </div>

          <div className="filter-group">
            <label>
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={(e) => setAutoScroll(e.target.checked)}
              />
              Auto-scroll
            </label>
          </div>

          <div className="action-buttons">
            <button onClick={exportLogs}>Export</button>
            <button onClick={clearLogs} className="danger">Clear</button>
          </div>
        </div>

        <div className="log-viewer-content">
          {filteredLogs.length === 0 ? (
            <div className="no-logs">No logs match the current filters</div>
          ) : (
            filteredLogs.map((log, index) => (
              <div key={index} className={`log-entry log-${log.level.toLowerCase()}`}>
                <div className="log-header">
                  <span className="log-timestamp">{formatTimestamp(log.timestamp)}</span>
                  <span 
                    className="log-level" 
                    style={{ color: getLevelColor(log.level) }}
                  >
                    {log.level}
                  </span>
                  {log.context && (
                    <span className="log-context">[{log.context}]</span>
                  )}
                  {log.userId && (
                    <span className="log-user">User: {log.userId}</span>
                  )}
                </div>
                <div className="log-message">{log.message}</div>
                {log.error && (
                  <div className="log-error">
                    <strong>Error:</strong> {log.error.message}
                    {log.error.stack && (
                      <details>
                        <summary>Stack Trace</summary>
                        <pre>{log.error.stack}</pre>
                      </details>
                    )}
                  </div>
                )}
                {log.metadata && Object.keys(log.metadata).length > 0 && (
                  <details className="log-metadata">
                    <summary>Metadata</summary>
                    <pre>{JSON.stringify(log.metadata, null, 2)}</pre>
                  </details>
                )}
                <div className="log-footer">
                  <span className="log-session">Session: {log.sessionId}</span>
                  <span className="log-url">{log.url}</span>
                </div>
              </div>
            ))
          )}
          <div ref={logsEndRef} />
        </div>
      </div>
    </div>
  );
};