export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL'
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string;
  error?: Error;
  metadata?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private sessionId: string;
  private isDevelopment = import.meta.env.DEV;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled Promise Rejection', 'global', event.reason, {
        promise: event.promise,
        type: 'unhandledrejection'
      });
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.error('JavaScript Error', 'global', event.error, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'javascript'
      });
    });

    // Handle React error boundaries (if needed)
    const originalConsoleError = console.error;
    console.error = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('React')) {
        this.error('React Error', 'react', new Error(args.join(' ')), {
          type: 'react',
          args: args
        });
      }
      originalConsoleError.apply(console, args);
    };
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: string,
    error?: Error,
    metadata?: Record<string, any>
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      error,
      metadata,
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Add user info if available
    try {
      const user = localStorage.getItem('user');
      if (user) {
        const userData = JSON.parse(user);
        entry.userId = userData.id?.toString();
      }
    } catch (e) {
      // Ignore parsing errors
    }

    return entry;
  }

  private addLog(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output in development
    if (this.isDevelopment) {
      this.outputToConsole(entry);
    }

    // Send to server for critical errors
    if (entry.level === LogLevel.ERROR || entry.level === LogLevel.FATAL) {
      this.sendToServer(entry);
    }
  }

  private outputToConsole(entry: LogEntry): void {
    const style = this.getConsoleStyle(entry.level);
    const prefix = `[${entry.timestamp}] [${entry.level}] [${entry.context || 'APP'}]`;
    
    console.group(`%c${prefix}`, style);
    console.log('Message:', entry.message);
    
    if (entry.error) {
      console.error('Error:', entry.error);
      if (entry.error.stack) {
        console.log('Stack:', entry.error.stack);
      }
    }
    
    if (entry.metadata) {
      console.log('Metadata:', entry.metadata);
    }
    
    console.log('Session ID:', entry.sessionId);
    console.log('URL:', entry.url);
    console.groupEnd();
  }

  private getConsoleStyle(level: LogLevel): string {
    const styles = {
      [LogLevel.DEBUG]: 'color: #888; font-weight: normal;',
      [LogLevel.INFO]: 'color: #007acc; font-weight: bold;',
      [LogLevel.WARN]: 'color: #ff8c00; font-weight: bold;',
      [LogLevel.ERROR]: 'color: #dc3545; font-weight: bold;',
      [LogLevel.FATAL]: 'color: #fff; background-color: #dc3545; font-weight: bold; padding: 2px 4px;'
    };
    return styles[level] || '';
  }

  private async sendToServer(entry: LogEntry): Promise<void> {
    try {
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      await fetch(`${apiUrl}/api/logs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken') || ''}`
        },
        body: JSON.stringify({
          ...entry,
          error: entry.error ? {
            name: entry.error.name,
            message: entry.error.message,
            stack: entry.error.stack
          } : undefined
        })
      });
    } catch (error) {
      // Silently fail to avoid infinite loops
      console.warn('Failed to send log to server:', error);
    }
  }

  // Public logging methods
  debug(message: string, context?: string, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context, undefined, metadata);
    this.addLog(entry);
  }

  info(message: string, context?: string, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context, undefined, metadata);
    this.addLog(entry);
  }

  warn(message: string, context?: string, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context, undefined, metadata);
    this.addLog(entry);
  }

  error(message: string, context?: string, error?: Error, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error, metadata);
    this.addLog(entry);
  }

  fatal(message: string, context?: string, error?: Error, metadata?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.FATAL, message, context, error, metadata);
    this.addLog(entry);
  }

  // API call logging
  logApiCall(method: string, url: string, status?: number, duration?: number, error?: Error): void {
    const level = error ? LogLevel.ERROR : (status && status >= 400 ? LogLevel.WARN : LogLevel.INFO);
    const message = `API ${method} ${url} - Status: ${status || 'Unknown'}`;
    
    this.addLog(this.createLogEntry(level, message, 'api', error, {
      method,
      url,
      status,
      duration,
      type: 'api_call'
    }));
  }

  // Authentication logging
  logAuth(action: string, success: boolean, error?: Error, metadata?: Record<string, any>): void {
    const level = success ? LogLevel.INFO : LogLevel.ERROR;
    const message = `Auth ${action} - ${success ? 'Success' : 'Failed'}`;
    
    this.addLog(this.createLogEntry(level, message, 'auth', error, {
      action,
      success,
      type: 'authentication',
      ...metadata
    }));
  }

  // Navigation logging
  logNavigation(from: string, to: string, metadata?: Record<string, any>): void {
    this.info(`Navigation: ${from} → ${to}`, 'navigation', {
      from,
      to,
      type: 'navigation',
      ...metadata
    });
  }

  // Component lifecycle logging
  logComponent(component: string, action: string, metadata?: Record<string, any>): void {
    this.debug(`Component ${component} - ${action}`, 'component', {
      component,
      action,
      type: 'component_lifecycle',
      ...metadata
    });
  }

  // Performance logging
  logPerformance(operation: string, duration: number, metadata?: Record<string, any>): void {
    const level = duration > 1000 ? LogLevel.WARN : LogLevel.DEBUG;
    this.addLog(this.createLogEntry(level, `Performance: ${operation} took ${duration}ms`, 'performance', undefined, {
      operation,
      duration,
      type: 'performance',
      ...metadata
    }));
  }

  // Get logs for debugging
  getLogs(level?: LogLevel, context?: string, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;
    
    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }
    
    if (context) {
      filteredLogs = filteredLogs.filter(log => log.context === context);
    }
    
    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }
    
    return filteredLogs;
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Clear logs
  clearLogs(): void {
    this.logs = [];
    this.info('Logs cleared', 'logger');
  }

  // Get error summary
  getErrorSummary(): { total: number; byLevel: Record<LogLevel, number>; recent: LogEntry[] } {
    const byLevel = {
      [LogLevel.DEBUG]: 0,
      [LogLevel.INFO]: 0,
      [LogLevel.WARN]: 0,
      [LogLevel.ERROR]: 0,
      [LogLevel.FATAL]: 0
    };

    this.logs.forEach(log => {
      byLevel[log.level]++;
    });

    const recent = this.logs
      .filter(log => log.level === LogLevel.ERROR || log.level === LogLevel.FATAL)
      .slice(-10);

    return {
      total: this.logs.length,
      byLevel,
      recent
    };
  }
}

// Create singleton instance
export const logger = new Logger();

// Export for debugging in console
if (typeof window !== 'undefined') {
  (window as any).logger = logger;
}