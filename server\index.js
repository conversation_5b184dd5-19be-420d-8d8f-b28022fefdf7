const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { Pool } = require('pg');
const { OAuth2Client } = require('google-auth-library');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

dotenv.config();

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Database connection (demo mode - will be replaced with real DB)
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'syra_db',
  user: process.env.DB_USER || 'username',
  password: process.env.DB_PASSWORD || 'Pu1UTYjaya25',
});

// Demo mode flag
const DEMO_MODE = true;

// Google OAuth client
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Demo data
const demoUsers = new Map();
const demoDomains = new Map();
const demoLogs = new Map();
const demoAttacks = new Map();

// Initialize database tables
async function initDatabase() {
  if (DEMO_MODE) {
    console.log('Running in demo mode - using in-memory data');
    return;
  }

  try {
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        google_id VARCHAR(255) UNIQUE,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        picture VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS domains (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        domain_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS security_logs (
        id SERIAL PRIMARY KEY,
        domain_id INTEGER REFERENCES domains(id),
        log_message TEXT NOT NULL,
        log_type VARCHAR(50) DEFAULT 'info',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await pool.query(`
      CREATE TABLE IF NOT EXISTS attacks (
        id SERIAL PRIMARY KEY,
        domain_id INTEGER REFERENCES domains(id),
        attack_type VARCHAR(100) NOT NULL,
        status VARCHAR(50) DEFAULT 'detected',
        severity VARCHAR(50) DEFAULT 'medium',
        detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        resolved_at TIMESTAMP,
        details TEXT
      )
    `);

    console.log('Database tables initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// Routes

// Google OAuth login
app.post('/api/auth/google', async (req, res) => {
  try {
    const { token } = req.body;

    if (DEMO_MODE) {
      // Demo mode - simulate successful authentication
      const demoUser = {
        id: 1,
        email: '<EMAIL>',
        name: 'Demo User',
        picture: 'https://via.placeholder.com/150'
      };

      const jwtToken = jwt.sign(
        { userId: demoUser.id, email: demoUser.email },
        process.env.JWT_SECRET || 'default-secret',
        { expiresIn: '24h' }
      );

      demoUsers.set(demoUser.id, demoUser);

      return res.json({
        success: true,
        token: jwtToken,
        user: demoUser
      });
    }

    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    const { sub: googleId, email, name, picture } = payload;

    // Check if user exists
    let userResult = await pool.query('SELECT * FROM users WHERE google_id = $1', [googleId]);

    if (userResult.rows.length === 0) {
      // Create new user
      userResult = await pool.query(
        'INSERT INTO users (google_id, email, name, picture) VALUES ($1, $2, $3, $4) RETURNING *',
        [googleId, email, name, picture]
      );
    } else {
      // Update existing user
      userResult = await pool.query(
        'UPDATE users SET name = $1, picture = $2, updated_at = CURRENT_TIMESTAMP WHERE google_id = $3 RETURNING *',
        [name, picture, googleId]
      );
    }

    const user = userResult.rows[0];

    // Generate JWT token
    const jwtToken = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'default-secret',
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      token: jwtToken,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        picture: user.picture
      }
    });
  } catch (error) {
    console.error('Google auth error:', error);
    res.status(401).json({ success: false, message: 'Authentication failed' });
  }
});

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'default-secret', (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Get user domains
app.get('/api/domains', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM domains WHERE user_id = $1 ORDER BY created_at DESC',
      [req.user.userId]
    );
    res.json({ success: true, domains: result.rows });
  } catch (error) {
    console.error('Error fetching domains:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Add new domain
app.post('/api/domains', authenticateToken, async (req, res) => {
  try {
    const { domainName } = req.body;
    
    const result = await pool.query(
      'INSERT INTO domains (user_id, domain_name) VALUES ($1, $2) RETURNING *',
      [req.user.userId, domainName]
    );
    
    res.json({ success: true, domain: result.rows[0] });
  } catch (error) {
    console.error('Error adding domain:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get security logs for a domain
app.get('/api/domains/:id/logs', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await pool.query(
      `SELECT sl.* FROM security_logs sl 
       JOIN domains d ON sl.domain_id = d.id 
       WHERE d.id = $1 AND d.user_id = $2 
       ORDER BY sl.created_at DESC`,
      [id, req.user.userId]
    );
    
    res.json({ success: true, logs: result.rows });
  } catch (error) {
    console.error('Error fetching logs:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get attacks for a domain
app.get('/api/domains/:id/attacks', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await pool.query(
      `SELECT a.* FROM attacks a 
       JOIN domains d ON a.domain_id = d.id 
       WHERE d.id = $1 AND d.user_id = $2 
       ORDER BY a.detected_at DESC`,
      [id, req.user.userId]
    );
    
    res.json({ success: true, attacks: result.rows });
  } catch (error) {
    console.error('Error fetching attacks:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'SYRA API is running' });
});

// Start server
app.listen(port, async () => {
  console.log(`SYRA Backend API running on port ${port}`);
  await initDatabase();
});
